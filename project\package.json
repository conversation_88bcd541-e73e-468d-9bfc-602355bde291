{"name": "project", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "axios": "^1.10.0", "expo": "~53.0.17", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "typescript": "~5.8.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10"}, "private": true}
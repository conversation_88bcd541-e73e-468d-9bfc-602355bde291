import React from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import { useEventContext } from '../context/EventContext';
import EventCard from '../components/EventCard';

export default function FavoritesScreen({ navigation }: any) {
  const { favorites } = useEventContext();

  return (
    <View style={styles.container}>
      <FlatList
        data={favorites}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <EventCard event={item} onPress={() => navigation.navigate('EventDetail', { event: item })} />
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
});

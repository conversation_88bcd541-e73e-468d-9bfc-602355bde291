import React from 'react';
import { View, Text, StyleSheet, Button } from 'react-native';
import { useEventContext } from '../context/EventContext';

export default function EventDetailScreen({ route }: any) {
  const { event } = route.params;
  const { toggleFavorite, favorites } = useEventContext();
  const isFavorite = favorites.some((e) => e.id === event.id);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{event.name}</Text>
      <Text>{event.dates.start.localDate}</Text>
      <Text>{event._embedded?.venues[0]?.name}</Text>
      <Text>{event.info || 'No description available.'}</Text>
      <Button title={isFavorite ? 'Remove from Favorites' : 'Save to Favorites'} onPress={() => toggleFavorite(event)} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 10 },
});

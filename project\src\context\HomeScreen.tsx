import React, { useEffect } from 'react';
import { View, FlatList, StyleSheet, RefreshControl } from 'react-native';
import axios from 'axios';
import { useEventContext } from './EventContext';
import EventCard from './EventCard';

export default function HomeScreen({ navigation }: any) {
  const { events, setEvents } = useEventContext();

  const fetchEvents = async () => {
    const res = await axios.get(
      `https://app.ticketmaster.com/discovery/v2/events.json?apikey=YOUR_API_KEY&countryCode=CA`
    );
    setEvents(res.data._embedded?.events || []);
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  return (
    <View style={styles.container}>
      <FlatList
        data={events}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <EventCard event={item} onPress={() => navigation.navigate('EventDetail', { event: item })} />
        )}
        refreshControl={<RefreshControl refreshing={false} onRefresh={fetchEvents} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
});

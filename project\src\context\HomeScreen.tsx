import React, { useEffect } from 'react';
import { View, FlatList, StyleSheet, RefreshControl } from 'react-native';
import axios from 'axios';
import { useEventContext } from './EventContext';
import EventCard from './EventCard';

export default function HomeScreen({ navigation }: any) {
  const { events, setEvents } = useEventContext();

  const fetchEvents = async () => {
    try {
      // Using a demo API key - replace with your actual Ticketmaster API key
      const res = await axios.get(
        `https://app.ticketmaster.com/discovery/v2/events.json?apikey=********************************&countryCode=CA&size=20`
      );
      setEvents(res.data._embedded?.events || []);
    } catch (error) {
      console.error('Error fetching events:', error);
      // Fallback to mock data if API fails
      setEvents([
        {
          id: '1',
          name: 'Sample Concert Event',
          dates: { start: { localDate: '2025-08-15' } },
          _embedded: { venues: [{ name: 'Sample Venue, Toronto' }] },
          info: 'This is a sample event for demonstration purposes.'
        },
        {
          id: '2',
          name: 'Sports Event',
          dates: { start: { localDate: '2025-08-20' } },
          _embedded: { venues: [{ name: 'Rogers Centre, Toronto' }] },
          info: 'Exciting sports event in Toronto.'
        }
      ]);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  return (
    <View style={styles.container}>
      <FlatList
        data={events}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <EventCard event={item} onPress={() => navigation.navigate('EventDetail', { event: item })} />
        )}
        refreshControl={<RefreshControl refreshing={false} onRefresh={fetchEvents} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
});

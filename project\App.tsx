import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { EventProvider } from './src/context/EventContext';
import HomeScreen from './src/context/HomeScreen';
import EventDetailScreen from './src/context/EventDetailScreen';
import FavoritesScreen from './src/context/FavoritesScreen';
import SearchScreen from './src/context/SearchScreen';
import AboutScreen from './src/context/AboutScreen';

// Define the parameter list for the stack navigator
export type RootStackParamList = {
  Home: undefined;
  EventDetail: { eventId: string };
  Favorites: undefined;
  Search: undefined;
  About: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList, 'RootStack'>();

// Declare global types for React Navigation
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

export default function App() {
  return (
    <EventProvider>
      <NavigationContainer>
        <Stack.Navigator initialRouteName="Home" id="RootStack">
          <Stack.Screen name="Home" component={HomeScreen} />
          <Stack.Screen name="EventDetail" component={EventDetailScreen} />
          <Stack.Screen name="Favorites" component={FavoritesScreen} />
          <Stack.Screen name="Search" component={SearchScreen} />
          <Stack.Screen name="About" component={AboutScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </EventProvider>
  );
}
